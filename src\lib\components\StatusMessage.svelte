<script lang="ts">
  import { statusMessages } from '../stores/statusStore';
</script>

<div class="status-messages-container">
  {#each $statusMessages as status (status.id)}
    <div class="status-message status-{status.type}">
      {status.text}
    </div>
  {/each}
</div>

<style>
  .status-messages-container {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
  }
  .status-message {
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 0.9em;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    min-width: 200px;
    text-align: center;
  }
  .status-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  .status-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  .status-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
  }
</style>
