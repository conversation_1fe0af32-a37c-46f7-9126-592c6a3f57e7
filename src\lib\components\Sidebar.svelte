<script lang="ts">
  import {
    appSettings,
    updateAppSetting,
    updateModelSelection,
    availableModels,
    currentModelDescription,
    switchAIProvider,
    settingsLoaded
  } from '../stores/unifiedSettingsStore';
  import type { AIProviderType } from '../types';
  import CustomModelSelect from './CustomModelSelect.svelte';

  // Available font options
  const fontFamilies = [
    { value: "Arial, sans-serif", name: "Arial" },
    { value: "'Courier New', Courier, monospace", name: "Courier New" },
    { value: "'Georgia', serif", name: "Georgia" },
    { value: "'Times New Roman', Times, serif", name: "Times New Roman" },
    { value: "'Verdana', Geneva, sans-serif", name: "<PERSON>erd<PERSON>" },
  ];

  // Available AI providers
  const aiProviders: { value: AIProviderType, name: string }[] = [
    { value: "gemini", name: "<PERSON>" },
    { value: "openai", name: "<PERSON><PERSON><PERSON>" },
    { value: "custom", name: "Custom (OpenAI-like)" },
  ];

  // Handle AI provider change
  const handleProviderChange = (event: Event) => {
    const newProvider = (event.target as HTMLSelectElement).value as AIProviderType;
    switchAIProvider(newProvider);
  };

  // Handle numeric input with validation
  const handleNumberInput = (key: string, min: number, max: number, defaultValue: number) =>
    (event: Event) => {
      const value = parseInt((event.target as HTMLInputElement).value) || defaultValue;
      updateAppSetting(key as any, Math.max(min, Math.min(max, value)));
    };
</script>

{#if $settingsLoaded}
<!-- Settings sidebar with font and AI configuration -->
<aside class="sidebar">
  <h3>Settings</h3>

  <!-- Font settings -->
  <div class="settings-group">
    <label for="fontFamily">Font Family:</label>
    <select
      id="fontFamily"
      bind:value={$appSettings.fontFamily}
      on:change={(e) => updateAppSetting('fontFamily', (e.target as HTMLSelectElement).value)}
    >
      {#each fontFamilies as font}
        <option value={font.value}>{font.name}</option>
      {/each}
    </select>
  </div>

  <div class="settings-group">
    <label for="fontSize">Font Size (px): (Current: {$appSettings.fontSize})</label>
    <input
      type="number"
      id="fontSize"
      min="8"
      max="72"
      bind:value={$appSettings.fontSize}
      on:change={handleNumberInput('fontSize', 8, 72, 16)}
    />
  </div>

  <h3>AI Configuration</h3>

  <!-- AI Provider selection -->
  <div class="settings-group">
    <label for="aiProvider">AI Provider:</label>
    <select id="aiProvider" on:change={handleProviderChange}>
      {#each aiProviders as provider}
        <option value={provider.value}>{provider.name}</option>
      {/each}
    </select>
  </div>

  <!-- AI Model selection -->
  <div class="settings-group">
    <label for="aiModel">AI Model:</label>
    {#if $appSettings.aiProvider === 'custom'}
      <!-- Custom model input with history -->
      <CustomModelSelect
        bind:value={$appSettings.aiModel}
        placeholder="Enter custom model name"
        on:input={(e) => updateAppSetting('aiModel', e.detail)}
        on:change={(e) => updateAppSetting('aiModel', e.detail)}
      />
    {:else}
      <!-- Predefined model selection -->
      <select
        id="aiModel"
        bind:value={$appSettings.aiModel}
        on:change={(e) => updateAppSetting('aiModel', (e.target as HTMLSelectElement).value)}
      >
        {#each $availableModels as model (model.id)}
          <option value={model.id}>
            {model.name} ({model.id.length > 15 ? model.id.substring(0,15)+'...' : model.id})
          </option>
        {/each}
      </select>
    {/if}

    <!-- Show model description for predefined models -->
    {#if $appSettings.aiProvider !== 'custom' && $currentModelDescription}
      <p class="model-description">{$currentModelDescription}</p>
    {/if}
  </div>

  <!-- Autocomplete context length setting -->
  <div class="settings-group">
    <label for="autocompleteContextLength">Autocomplete Context Length:</label>
    <input
      type="number"
      id="autocompleteContextLength"
      min="100"
      max="10000"
      step="100"
      bind:value={$appSettings.autocompleteContextLength}
      on:change={handleNumberInput('autocompleteContextLength', 100, 10000, 1000)}
    />
    <p class="setting-description">
      Number of characters to use as context for autocomplete (100-10000)
    </p>
  </div>
</aside>
{:else}
<!-- Loading state -->
<aside class="sidebar">
  <h3>Settings</h3>
  <div class="loading-placeholder">Loading settings...</div>
</aside>
{/if}

<style>
  .sidebar {
    width: 280px;
    background-color: #e9e9e9;
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
    flex-shrink: 0;
    border-left: 1px solid #ccc;
  }
  .sidebar h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #ccc;
    padding-bottom: 5px;
  }
  .settings-group {
    margin-bottom: 20px;
  }
  .settings-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 0.9em;
  }
  .settings-group select,
  .settings-group input[type="number"] {
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 3px;
    background-color: #fff;
  }
  .model-description,
  .setting-description {
    font-size: 0.85em;
    margin-top: 5px;
    color: #555;
    min-height: 1.2em;
    line-height: 1.3;
  }
   /* Button styles removed - no longer needed since settings auto-save */

  .loading-placeholder {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
  }
</style>
