import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, fireEvent } from '@testing-library/svelte';
import Sidebar from '$lib/components/Sidebar.svelte';
import { initializeAllSettings, switchAIProvider, updateModelSelection, appSettings } from '$lib/stores/unifiedSettingsStore';
import { get } from 'svelte/store';

// Mock the unifiedSettingsStore
vi.mock('$lib/stores/unifiedSettingsStore', async () => {
    const originalModule = await vi.importActual('$lib/stores/unifiedSettingsStore');
    return {
        ...originalModule,
        initializeAllSettings: vi.fn(),
    };
});

describe('Sidebar UI Behavior', () => {
    beforeEach(async () => {
        // Reset the store before each test
        appSettings.set({
            fontFamily: 'Arial, sans-serif',
            fontSize: '16',
            aiProvider: 'openai',
            aiModel: 'gpt-4o-mini',
            autocompleteContextLength: 1000,
        });
        await initializeAllSettings();
    });

    it('should update the model dropdown when switching providers', async () => {
        const { container } = render(Sidebar);

        // 1. Set Gemini model
        switchAIProvider('gemini');
        updateModelSelection('gemini-1.5-pro');
        await new Promise(resolve => setTimeout(resolve, 0)); // Wait for UI to update

        // 2. Switch to OpenAI
        const providerSelect = container.querySelector('#aiProvider');
        await fireEvent.change(providerSelect, { target: { value: 'openai' } });
        await new Promise(resolve => setTimeout(resolve, 0)); // Wait for UI to update

        // 3. Verify OpenAI model is the default
        let modelSelect = container.querySelector('#aiModel') as HTMLSelectElement;
        expect(modelSelect.value).toBe('gpt-4o-mini');

        // 4. Select a different OpenAI model
        await fireEvent.change(modelSelect, { target: { value: 'gpt-4o' } });
        await new Promise(resolve => setTimeout(resolve, 0)); // Wait for UI to update
        expect(get(appSettings).aiModel).toBe('gpt-4o');

        // 5. Switch back to Gemini
        await fireEvent.change(providerSelect, { target: { value: 'gemini' } });
        await new Promise(resolve => setTimeout(resolve, 0)); // Wait for UI to update

        // 6. Verify Gemini model is restored
        modelSelect = container.querySelector('#aiModel') as HTMLSelectElement;
        expect(modelSelect.value).toBe('gemini-1.5-pro');
    });
});
