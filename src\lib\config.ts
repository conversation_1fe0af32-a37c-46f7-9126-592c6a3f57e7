import type { AppSettings, ApiKeys, ProviderSpecificModels } from './types';

// Model configuration interface
export interface ModelConfig {
    id: string;
    name: string;
}

// Application constants
export const AUTOCOMPLETE_CONTEXT_LENGTH = 1000;
export const API_TIMEOUT = 30000; // 30 seconds
export const OPENROUTER_HEADERS = {
    'HTTP-Referer': 'https://ai-notepad-app.local',
    'X-Title': 'AI Notepad'
};

// Available models for each provider
export const AVAILABLE_MODELS: Record<string, ModelConfig[]> = {
    gemini: [
        { id: "gemini-2.5-flash-preview-04-17", name: "Gemini 2.5 Flash (Preview 04-17) - Very Fast, Experimental" },
        { id: "gemini-2.5-pro-preview-05-06", name: "Gemini 2.5 Pro (Preview 05-06) - Powerful, Experimental" },
        { id: "gemini-2.0-flash", name: "Gemini 2.0 Flash - Fast, Balanced" },
        { id: "gemini-2.0-flash-lite", name: "Gemini 2.0 Flash Lite - Very Fast, Lightweight" },
        { id: "gemini-1.5-flash", name: "Gemini 1.5 Flash - Fast, Efficient, Good Value" },
        { id: "gemini-1.5-flash-8b", name: "Gemini 1.5 Flash (8B) - Compact & Fast" },
        { id: "gemini-1.5-pro", name: "Gemini 1.5 Pro - Advanced, Large Context" },
    ],
    openai: [
        { id: "gpt-4o-mini", name: "GPT-4o mini - Fast, Cost-Effective, Multimodal" },
        { id: "gpt-4o", name: "GPT-4o - Flagship, Intelligent, Multimodal" },
        { id: "gpt-3.5-turbo", name: "GPT-3.5 Turbo - Fast, Affordable, Text-Focused" },
    ],
    custom: [] // Custom models are entered by user
};

// Default application settings
export const DEFAULT_APP_SETTINGS: AppSettings = {
    fontFamily: 'Arial, sans-serif',
    fontSize: '16',
    aiProvider: 'openai',
    aiModel: AVAILABLE_MODELS.openai[0].id,
    autocompleteContextLength: AUTOCOMPLETE_CONTEXT_LENGTH,
    sidebarVisible: true
};

// Default API keys (empty)
export const DEFAULT_API_KEYS: ApiKeys = {
    gemini: '',
    openai: '',
    customUrl: '',
    customKey: ''
};

// Default provider-specific models
export const DEFAULT_PROVIDER_SPECIFIC_MODELS: ProviderSpecificModels = {
    gemini: AVAILABLE_MODELS.gemini[0].id,
    openai: AVAILABLE_MODELS.openai[0].id,
    custom: ''
};

// Storage keys for localStorage
export const STORAGE_KEYS = {
    NOTEPAD_CONTENT: 'aiNotepadSvelteContent',
    API_KEYS: 'aiNotepadSvelteApiKeys',
    APP_SETTINGS: 'aiNotepadSvelteSettings',
    CUSTOM_MODEL_HISTORY: 'aiNotepadSvelteCustomModelHistory',
    PROVIDER_SPECIFIC_MODELS: 'aiNotepadSvelteProviderSpecificModels'
} as const;
