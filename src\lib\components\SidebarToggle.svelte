<script lang="ts">
  import { appSettings, updateAppSetting } from '../stores/unifiedSettingsStore';

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    updateAppSetting('sidebarVisible', !$appSettings.sidebarVisible);
  };
</script>

<!-- Sidebar toggle button -->
<button
  class="sidebar-toggle"
  on:click={toggleSidebar}
  title={$appSettings.sidebarVisible ? 'Hide sidebar' : 'Show sidebar'}
>
  {$appSettings.sidebarVisible ? '→' : '←'}
</button>

<style>
  .sidebar-toggle {
    position: absolute;
    top: 30px;
    left: -40px;
    width: 40px;
    height: 40px;
    background-color: #d0d0d0;
    color: #666;
    border: none;
    border-radius: 20px 0 0 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    line-height: 1;
  }

  .sidebar-toggle:hover {
    background-color: #c0c0c0;
  }

  .sidebar-toggle:active {
    background-color: #b0b0b0;
  }
</style>
