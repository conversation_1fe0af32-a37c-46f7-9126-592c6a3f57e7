html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #f0f0f0;
    color: #333;
    overflow: hidden;
}

* {
    box-sizing: border-box;
}

::selection {
  background-color: #b3d4fc; /* A light blue, common for selections */
  color: #000; /* Ensure text is readable */
  /* text-shadow: none; */ /* Optional: remove text shadow if any */
}

/* For Firefox */
::-moz-selection {
  background-color: #b3d4fc;
  color: #000;
  /* text-shadow: none; */
}

:global(.notepad-editor .ai-generated) {
  background-color: #e6f7ff; /* Example: light blue background */
  /* border: 1px dashed #91d5ff; */
  padding: 1px 3px;
  border-radius: 3px;
  /* You can add more distinct styling here */
}

/* If you want AI text to have a different selection color */
/* Note: Styling ::selection based on parent class is not universally supported */
/* This might only work if the selection is *entirely* within the .ai-generated span */
:global(.notepad-editor .ai-generated::selection) {
  background-color: #ffcc80; /* Example: orange selection for AI text */
  color: black;
}
:global(.notepad-editor .ai-generated::-moz-selection) {
  background-color: #ffcc80;
  color: black;
}