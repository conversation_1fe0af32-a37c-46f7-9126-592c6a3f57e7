import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import { 
    appSettings, 
    lastSelectedModels, 
    lastCustomModels,
    switchAIProvider,
    initializeAllSettings
} from './unifiedSettingsStore';
import type { AIProviderType } from '../types';

// Mock browser environment
vi.mock('$app/environment', () => ({
    browser: true
}));

// Mock localStorage
const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
});

describe('Provider Switching with Model Persistence', () => {
    beforeEach(() => {
        // Clear all mocks
        vi.clearAllMocks();
        localStorageMock.getItem.mockReturnValue(null);
        
        // Reset stores to default state
        appSettings.set({
            fontFamily: 'Arial, sans-serif',
            fontSize: '16',
            aiProvider: 'openai',
            aiModel: 'gpt-4o-mini',
            autocompleteContextLength: 1000
        });
        
        lastSelectedModels.set({
            gemini: 'gemini-1.5-flash',
            openai: 'gpt-4o-mini',
            custom: ''
        });
        
        lastCustomModels.set({
            gemini: '',
            openai: '',
            custom: ''
        });
    });

    it('should preserve OpenAI model when switching to Gemini and back', () => {
        // Start with OpenAI and set a specific model
        appSettings.update(settings => ({ ...settings, aiProvider: 'openai', aiModel: 'gpt-4o' }));
        
        // Switch to Gemini
        switchAIProvider('gemini');
        
        let currentSettings = get(appSettings);
        expect(currentSettings.aiProvider).toBe('gemini');
        expect(currentSettings.aiModel).toBe('gemini-1.5-flash'); // Should restore saved Gemini model
        
        // Verify OpenAI model was saved
        let savedModels = get(lastSelectedModels);
        expect(savedModels.openai).toBe('gpt-4o');
        
        // Switch back to OpenAI
        switchAIProvider('openai');
        
        currentSettings = get(appSettings);
        expect(currentSettings.aiProvider).toBe('openai');
        expect(currentSettings.aiModel).toBe('gpt-4o'); // Should restore the saved OpenAI model
    });

    it('should preserve Gemini model when switching to OpenAI and back', () => {
        // Start with Gemini and set a specific model
        appSettings.update(settings => ({ ...settings, aiProvider: 'gemini', aiModel: 'gemini-1.5-pro' }));
        
        // Switch to OpenAI
        switchAIProvider('openai');
        
        let currentSettings = get(appSettings);
        expect(currentSettings.aiProvider).toBe('openai');
        expect(currentSettings.aiModel).toBe('gpt-4o-mini'); // Should restore saved OpenAI model
        
        // Verify Gemini model was saved
        let savedModels = get(lastSelectedModels);
        expect(savedModels.gemini).toBe('gemini-1.5-pro');
        
        // Switch back to Gemini
        switchAIProvider('gemini');
        
        currentSettings = get(appSettings);
        expect(currentSettings.aiProvider).toBe('gemini');
        expect(currentSettings.aiModel).toBe('gemini-1.5-pro'); // Should restore the saved Gemini model
    });

    it('should handle custom provider separately from predefined providers', () => {
        // Start with OpenAI
        appSettings.update(settings => ({ ...settings, aiProvider: 'openai', aiModel: 'gpt-4o' }));
        
        // Switch to custom
        switchAIProvider('custom');
        
        let currentSettings = get(appSettings);
        expect(currentSettings.aiProvider).toBe('custom');
        expect(currentSettings.aiModel).toBe(''); // Should be empty for custom initially
        
        // Verify OpenAI model was saved to lastSelectedModels
        let savedModels = get(lastSelectedModels);
        expect(savedModels.openai).toBe('gpt-4o');
        
        // Set a custom model
        appSettings.update(settings => ({ ...settings, aiModel: 'my-custom-model' }));
        
        // Switch back to OpenAI
        switchAIProvider('openai');
        
        currentSettings = get(appSettings);
        expect(currentSettings.aiProvider).toBe('openai');
        expect(currentSettings.aiModel).toBe('gpt-4o'); // Should restore OpenAI model
        
        // Verify custom model was saved to lastCustomModels
        let customModels = get(lastCustomModels);
        expect(customModels.custom).toBe('my-custom-model');
        
        // Switch back to custom
        switchAIProvider('custom');
        
        currentSettings = get(appSettings);
        expect(currentSettings.aiProvider).toBe('custom');
        expect(currentSettings.aiModel).toBe('my-custom-model'); // Should restore custom model
    });

    it('should use default model when no saved model exists', () => {
        // Clear saved models
        lastSelectedModels.set({
            gemini: '',
            openai: '',
            custom: ''
        });
        
        // Start with OpenAI
        appSettings.update(settings => ({ ...settings, aiProvider: 'openai', aiModel: 'gpt-4o' }));
        
        // Switch to Gemini (no saved model)
        switchAIProvider('gemini');
        
        let currentSettings = get(appSettings);
        expect(currentSettings.aiProvider).toBe('gemini');
        expect(currentSettings.aiModel).toBe('gemini-1.5-flash'); // Should use first available model
    });
});
