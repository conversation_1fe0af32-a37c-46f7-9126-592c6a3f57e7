<script lang="ts">
  import { onMount } from 'svelte';
  import Header from '$lib/components/Header.svelte';
  import StatusMessage from '$lib/components/StatusMessage.svelte';
  import { initializeAllSettings } from '$lib/stores/unifiedSettingsStore';

  import '../app.css';

  // Initialize settings immediately when script runs (before component mount)
  if (typeof window !== 'undefined') {
    initializeAllSettings();
  }

  onMount(() => {
    // Settings should already be initialized
  });
</script>

<!-- Always render layout components -->
<div class="app-container">
  <Header />
  <slot />
  <StatusMessage />
</div>

<style>
  .app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }




</style>