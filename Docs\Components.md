# Components Documentation

## Overview

All components have been simplified for better readability and maintainability. Each component has a single responsibility and clear interfaces.

## Header Component (`Header.svelte`)

### Purpose
Displays the application header with logo and API keys access button.

### Props
None

### State
- `showApiKeysModal: boolean` - Controls modal visibility

### Functions
- `openApiKeysModal()` - Opens the API keys configuration modal
- `closeApiKeysModal()` - Closes the API keys configuration modal

### Features
- Simple, clean header design
- Single button for API key management
- Modal state management

### Usage
```svelte
<Header />
```

---

## Sidebar Component (`Sidebar.svelte`)

### Purpose
Displays application settings including font configuration and AI provider/model selection.

### Props
None

### State
- Uses `appSettings` from unified settings store
- Conditionally rendered based on `sidebarVisible` setting

### Features
- Font family and size configuration
- AI provider selection (Gemini, OpenAI, Custom)
- Model selection with provider-specific options
- Custom model input with history
- Autocomplete context length setting
- Auto-save functionality

### Usage
```svelte
<Sidebar />
```

---

## SidebarToggle Component (`SidebarToggle.svelte`)

### Purpose
Provides a toggle button to show/hide the sidebar, positioned outside the sidebar area.

### Props
None

### State
- Reads `sidebarVisible` from unified settings store
- Updates setting via `updateAppSetting`

### Features
- Square button with rounded left corners
- Positioned absolutely outside sidebar area
- Always visible (even when sidebar is hidden)
- Directional arrow indicating current state
- Hover effects consistent with design system

### Styling
- Background: Accent green (#4CAF50)
- Size: 40x40px square
- Border radius: 20px on left side only
- Position: Absolute, 20px from top, -40px from left
- Z-index: 10 to stay above other content

### Usage
```svelte
<SidebarToggle />
```

---

### Purpose
Provides settings interface for font, AI provider, and model configuration.

### Dependencies
- `unifiedSettingsStore` - For all settings management
- `CustomModelSelect` - For custom model input

### State
All state managed through stores, no local component state.

### Functions
- `handleProviderChange(event)` - Switches AI provider and restores appropriate model
- `handleNumberInput(key, min, max, default)` - Validates and updates numeric settings

### Features
- Font family and size configuration
- AI provider selection (OpenAI, Gemini, Custom)
- Model selection with provider-specific options
- Autocomplete context length setting
- Auto-save on all changes
- Loading state handling

### Settings Available
1. **Font Settings**
   - Font family (Arial, Courier New, Georgia, Times New Roman, Verdana)
   - Font size (8-72px with validation)

2. **AI Configuration**
   - Provider selection with automatic model restoration
   - Model selection (predefined or custom)
   - Context length for autocomplete (100-10000 characters)

### Edge Cases
- Settings not loaded: Shows loading placeholder
- Invalid numeric input: Clamps to valid range
- Provider switch: Preserves last selected model per provider

---

## Notepad Component (`Notepad.svelte`)

### Purpose
Main text editing interface with AI-powered features.

### Dependencies
- `unifiedSettingsStore` - For settings and content
- `apiService` - For AI operations
- `textUtils` - For text manipulation
- `ThinkingIndicator` - For loading states

### State
- `editorElement: HTMLTextAreaElement` - Reference to textarea
- `isLoading: boolean` - AI operation status
- `thinkingMap: Map<string, string>` - Maps AI responses to thinking content
- `currentThinking: string | null` - Currently displayed thinking
- `showThinkingPanel: boolean` - Thinking panel visibility

### Core Functions

#### `callAI(prompt, onSuccess)`
Helper function that:
- Manages loading state
- Calls AI service
- Stores thinking content
- Executes success callback

#### `handleAutocomplete()`
- Gets context from current text
- Generates continuation prompt
- Inserts response at end of text

#### `handleAnswerSelection()`
- Requires text selection
- Uses context before/after selection
- Replaces selected text with AI response

#### `handleInsertAtCursor()`
- Uses context around cursor position
- Inserts AI response at cursor
- Maintains cursor positioning

#### `insertTextAtCursor(text)`
- Inserts text at current cursor position
- Selects inserted text
- Updates notepad content

### AI Features

1. **Autocomplete**
   - Uses last N characters as context
   - Appends continuation to text
   - Preserves writing style

2. **Replace Selection**
   - Replaces selected text
   - Uses surrounding context
   - Maintains text flow

3. **Insert at Cursor**
   - Fills gaps in text
   - Uses before/after context
   - Smart positioning

### Thinking Process
- AI responses can include `<think>` tags
- Thinking content extracted and stored
- Accessible via thinking panel
- Shows AI reasoning process

### Edge Cases
- Empty notepad: Shows welcome message
- No selection for answer: Shows error
- Loading state: Disables all AI buttons
- Settings not loaded: Shows loading placeholder

---

## CustomModelSelect Component (`CustomModelSelect.svelte`)

### Purpose
Provides input field with dropdown for custom model selection and history.

### Props
- `value: string` - Current model value
- `placeholder: string` - Input placeholder text

### Events
- `input` - Fired on value change
- `change` - Fired on selection completion

### State
- `isOpen: boolean` - Dropdown visibility
- `inputElement: HTMLInputElement` - Input reference
- `dropdownElement: HTMLDivElement` - Dropdown reference

### Features
- Auto-complete from history
- Dropdown with recent models
- Remove models from history
- Keyboard navigation (Escape, Arrow Down)
- Click outside to close
- New model suggestions

### Functions
- `handleInput(event)` - Updates value and opens dropdown
- `selectModel(model)` - Selects model and closes dropdown
- `removeModel(event, model)` - Removes model from history
- `toggleDropdown()` - Toggles dropdown visibility
- `handleKeydown(event)` - Keyboard navigation
- `handleClickOutside(event)` - Closes on outside click

### History Management
- Filters history based on input
- Shows recent models first
- Allows removal of individual models
- Suggests new model if not in history

---

## ApiKeysModal Component (`ApiKeysModal.svelte`)

### Purpose
Modal interface for configuring API keys for all providers.

### Props
- `isOpen: boolean` - Modal visibility

### Events
- `close` - Fired when modal closes

### State
- `activeTab: 'gemini' | 'openai' | 'custom'` - Current tab

### Features
- Tabbed interface for each provider
- Auto-save on input changes
- Password field security
- External links to get API keys
- Keyboard shortcuts (Escape to close)
- Click outside to close

### Tabs

1. **OpenAI Tab**
   - API key input
   - Link to OpenAI platform

2. **Gemini Tab**
   - API key input
   - Link to Google AI Studio

3. **Custom Tab**
   - Base URL input
   - API key input
   - OpenRouter example

### Functions
- `closeModal()` - Closes modal and dispatches event
- `handleBackdropClick(event)` - Closes on backdrop click
- `handleKeydown(event)` - Handles Escape key
- `handleInput(key)` - Returns input handler for specific key

### Accessibility
- ARIA labels and roles
- Keyboard navigation
- Focus management
- Screen reader support

### Security
- Password input types
- No key exposure in DOM
- Secure external links
